import { defineStore } from 'pinia'
import { ref } from 'vue'
import client from '@/api/client'
import { normalizeApiResponse, formatErrorMessage } from '@/utils/backwardCompatibility'

export const useVideoStore = defineStore('videos', () => {
  // 状态
  const videos = ref([])
  const currentVideo = ref(null)

  // 操作
  const fetchVideos = async () => {
    try {
      const response = await client.get('/videos')
      // Normalize response for backward compatibility
      const normalizedVideos = normalizeApiResponse(response, 'video')
      videos.value = normalizedVideos
      return normalizedVideos
    } catch (error) {
      console.error('Failed to fetch videos:', error)
      throw new Error(formatErrorMessage(error, '视频列表'))
    }
  }

  const fetchVideo = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}`)
      // Normalize response for backward compatibility
      const normalizedVideo = normalizeApiResponse(response, 'video')
      currentVideo.value = normalizedVideo
      return normalizedVideo
    } catch (error) {
      console.error('Failed to fetch video:', error)
      throw new Error(formatErrorMessage(error, '视频'))
    }
  }

  const startAnalysis = async (videoId) => {
    try {
      const response = await client.post(`/videos/${videoId}/analyze`)

      // 更新本地视频状态
      const video = videos.value.find(v => v.id === videoId)
      if (video) {
        video.status = 'analyzing'
      }

      if (currentVideo.value && currentVideo.value.id === videoId) {
        currentVideo.value.status = 'analyzing'
      }

      return response
    } catch (error) {
      console.error('Failed to start analysis:', error)
      throw error
    }
  }

  const getAnalysisResults = async (videoId) => {
    try {
      const response = await client.get(`/analysis/${videoId}`)
      // Handle new analysis result format with enhanced processing time fields
      if (Array.isArray(response)) {
        return response.map(result => ({
          ...result,
          // Ensure backward compatibility while using new fields
          processing_duration: result.processing_duration_seconds || result.processing_time || 0,
          started_at: result.started_at,
          completed_at: result.completed_at,
          // Keep original fields for compatibility
          processing_time: result.processing_time
        }))
      }
      return response
    } catch (error) {
      console.error('Failed to get analysis results:', error)
      // Enhanced error handling for analysis results
      if (error.response?.status === 404) {
        console.warn(`Analysis results not found for video ${videoId}`)
        return [] // Return empty array for missing results
      }
      throw error
    }
  }

  const getAnalysisStep = async (videoId, step) => {
    try {
      const response = await client.get(`/analysis/${videoId}/step/${step}`)
      return response
    } catch (error) {
      console.error('Failed to get analysis step:', error)
      throw error
    }
  }

  // 获取场景检测结果
  const getScenes = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}/scenes`)
      return response
    } catch (error) {
      console.error('Failed to get scenes:', error)
      throw error
    }
  }

  // 触发场景检测
  const detectScenes = async (videoId, options = {}) => {
    try {
      const response = await client.post(`/videos/${videoId}/scenes/detect`, options)
      return response
    } catch (error) {
      console.error('Failed to detect scenes:', error)
      throw error
    }
  }

  // 获取比特率统计
  const getBitrateStats = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}/bitrate-stats`)
      return response
    } catch (error) {
      console.error('Failed to get bitrate stats:', error)
      throw error
    }
  }

  // 获取比特率图表
  const getBitratePlot = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}/bitrate-plot`)
      return response
    } catch (error) {
      console.error('Failed to get bitrate plot:', error)
      throw error
    }
  }

  // 重试视频分析
  const retryAnalysis = async (videoId) => {
    try {
      const response = await client.post(`/videos/${videoId}/retry`)

      // 更新本地视频状态
      const video = videos.value.find(v => v.id === videoId)
      if (video) {
        video.status = 'analyzing'
      }

      if (currentVideo.value && currentVideo.value.id === videoId) {
        currentVideo.value.status = 'analyzing'
      }

      return response
    } catch (error) {
      console.error('Failed to retry analysis:', error)
      throw error
    }
  }

  // 提取关键帧 (新的API端点)
  const extractKeyFrames = async (videoId) => {
    try {
      const response = await client.post(`/videos/${videoId}/extract-key-frames`)
      // Response now includes key_frames_only flag and uses organized file structure
      return {
        ...response,
        key_frames_only: response.key_frames_only || true,
        frame_count: response.frame_count || 0,
        frame_files: response.frame_files || []
      }
    } catch (error) {
      console.error('Failed to extract key frames:', error)
      // Enhanced error handling for key frame extraction
      if (error.response?.status === 404) {
        throw new Error(`视频 ${videoId} 不存在`)
      } else if (error.response?.status === 500) {
        const detail = error.response?.data?.detail || '关键帧提取失败'
        throw new Error(`服务器错误: ${detail}`)
      }
      throw new Error('关键帧提取失败，请稍后重试')
    }
  }

  // 提取音频 (使用新的组织化文件结构)
  const extractAudio = async (videoId) => {
    try {
      const response = await client.post(`/videos/${videoId}/extract-audio`)
      // Audio files now stored in organized structure
      return response
    } catch (error) {
      console.error('Failed to extract audio:', error)
      // Enhanced error handling for audio extraction
      if (error.response?.status === 404) {
        throw new Error(`视频 ${videoId} 不存在`)
      } else if (error.response?.status === 500) {
        const detail = error.response?.data?.detail || '音频提取失败'
        throw new Error(`服务器错误: ${detail}`)
      }
      throw new Error('音频提取失败，请稍后重试')
    }
  }

  // 获取视频帧列表
  const getVideoFrames = async (videoId) => {
    try {
      const response = await client.get(`/videos/${videoId}/frames`)
      // 返回完整的响应数据
      return response
    } catch (error) {
      console.error('Failed to get video frames:', error)
      if (error.response?.status === 404) {
        throw new Error(`视频 ${videoId} 不存在`)
      } else if (error.response?.status === 500) {
        const detail = error.response?.data?.detail || '获取帧列表失败'
        throw new Error(`服务器错误: ${detail}`)
      }
      throw new Error('获取帧列表失败，请稍后重试')
    }
  }

  return {
    // 状态
    videos,
    currentVideo,

    // 操作
    fetchVideos,
    fetchVideo,
    startAnalysis,
    getAnalysisResults,
    getAnalysisStep,
    getScenes,
    detectScenes,
    getBitrateStats,
    getBitratePlot,
    retryAnalysis,
    // 新的API方法
    extractKeyFrames,
    extractAudio,
    getVideoFrames
  }
})
