<template>
  <div class="bg-gray-50 rounded-lg p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-3">音频信息</h3>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
      <span class="ml-2 text-sm text-gray-500">加载音频数据中...</span>
    </div>

    <!-- 音频信息内容 -->
    <div v-else-if="audioTracks.length > 0" class="space-y-4">
      <!-- 音频轨道信息 -->
      <div class="bg-white rounded-lg p-4 border">
        <h4 class="text-md font-medium text-gray-900 mb-3">音频轨道</h4>
        <div class="space-y-3">
          <div
            v-for="(track, index) in audioTracks"
            :key="track.id"
            class="border rounded-lg p-3"
            :class="{ 'border-primary-500 bg-primary-50': selectedTrack?.id === track.id }"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <button
                  @click="selectTrack(track)"
                  class="text-sm font-medium text-primary-600 hover:text-primary-700"
                >
                  轨道 {{ track.stream_index + 1 }}
                </button>
                <span v-if="selectedTrack?.id === track.id" class="text-xs bg-primary-100 text-primary-800 px-2 py-0.5 rounded">
                  当前选中
                </span>
              </div>
              <div class="text-xs text-gray-500">
                {{ formatDuration(track.duration) }}
              </div>
            </div>
            
            <!-- 音频基础信息 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600">
              <div>
                <span class="text-gray-500">编码:</span>
                <span class="font-medium">{{ track.codec_name }}</span>
              </div>
              <div>
                <span class="text-gray-500">采样率:</span>
                <span class="font-medium">{{ track.sample_rate }} Hz</span>
              </div>
              <div>
                <span class="text-gray-500">声道:</span>
                <span class="font-medium">{{ track.channels }} 声道</span>
              </div>
              <div>
                <span class="text-gray-500">比特率:</span>
                <span class="font-medium">{{ formatBitrate(track.bit_rate) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 音频播放器 -->
      <div v-if="selectedTrack" class="bg-white rounded-lg p-4 border">
        <h4 class="text-md font-medium text-gray-900 mb-3">音频播放器</h4>
        <div class="space-y-3">
          <!-- HTML5 音频播放器 -->
          <audio
            ref="audioPlayer"
            :src="getAudioUrl(selectedTrack.file_path)"
            controls
            class="w-full"
            @timeupdate="onTimeUpdate"
            @loadedmetadata="onAudioLoaded"
            @error="onAudioError"
          >
            您的浏览器不支持音频播放器
          </audio>
          
          <!-- 播放控制信息 -->
          <div class="flex items-center justify-between text-sm text-gray-600">
            <span>{{ formatTime(currentTime) }} / {{ formatTime(audioDuration) }}</span>
            <div class="flex items-center space-x-2">
              <button
                @click="togglePlayback"
                class="px-3 py-1 bg-primary-600 hover:bg-primary-700 text-white rounded text-xs"
              >
                {{ isPlaying ? '暂停' : '播放' }}
              </button>
              <button
                @click="seekToTime(0)"
                class="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-xs"
              >
                重播
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 字幕信息 -->
      <div class="bg-white rounded-lg p-4 border">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-md font-medium text-gray-900">字幕信息</h4>
          <button
            v-if="subtitles.length > 0"
            @click="toggleSubtitlesExpanded"
            class="text-sm text-primary-600 hover:text-primary-700"
          >
            {{ subtitlesExpanded ? '收起' : '展开' }}
          </button>
        </div>
        
        <div v-if="subtitles.length === 0" class="text-center text-gray-500 py-4">
          <p class="text-sm">暂无字幕数据</p>
          <p class="text-xs mt-1">可以上传字幕文件或生成自动字幕</p>
        </div>
        
        <div v-else class="space-y-3">
          <!-- 字幕列表概览 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div
              v-for="subtitle in subtitles"
              :key="subtitle.id"
              class="border rounded p-2 cursor-pointer hover:border-primary-300"
              :class="{ 'border-primary-500 bg-primary-50': selectedSubtitle?.id === subtitle.id }"
              @click="selectSubtitle(subtitle)"
            >
              <div class="flex items-center justify-between">
                <div>
                  <span class="text-sm font-medium">{{ getSubtitleTypeText(subtitle.subtitle_type) }}</span>
                  <span class="text-xs text-gray-500 ml-2">{{ subtitle.language }}</span>
                </div>
                <div class="text-xs text-gray-500">
                  {{ Math.round((subtitle.confidence || 0) * 100) }}% 置信度
                </div>
              </div>
            </div>
          </div>
          
          <!-- 字幕内容详情 -->
          <div v-if="subtitlesExpanded && selectedSubtitle && subtitleContent.length > 0" class="border-t pt-3">
            <h5 class="text-sm font-medium text-gray-900 mb-2">字幕内容</h5>
            <div class="max-h-64 overflow-y-auto space-y-2">
              <div
                v-for="(item, index) in subtitleContent"
                :key="index"
                class="p-2 bg-gray-50 rounded text-sm"
              >
                <div class="flex items-center justify-between mb-1">
                  <span class="text-xs text-gray-500">
                    {{ formatTime(item.start_time) }} - {{ formatTime(item.end_time) }}
                  </span>
                  <span v-if="item.confidence" class="text-xs text-gray-500">
                    {{ Math.round(item.confidence * 100) }}%
                  </span>
                </div>
                <p class="text-gray-700">{{ item.text }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无音频数据 -->
    <div v-else class="text-center text-gray-500 py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12a1 1 0 01-1-1V9a1 1 0 011-1h1a1 1 0 011 1v2a1 1 0 01-1 1H9z" />
      </svg>
      <p class="text-sm">暂无音频数据</p>
      <p class="text-xs mt-1">请先提取音频轨道</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useVideoStore } from '@/stores/videos'

const props = defineProps({
  videoId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['time-changed', 'track-selected'])

const videoStore = useVideoStore()

// 响应式数据
const loading = ref(false)
const audioTracks = ref([])
const subtitles = ref([])
const selectedTrack = ref(null)
const selectedSubtitle = ref(null)
const subtitleContent = ref([])
const subtitlesExpanded = ref(false)
const audioPlayer = ref(null)
const currentTime = ref(0)
const audioDuration = ref(0)
const isPlaying = ref(false)

// 方法
const loadAudioData = async () => {
  try {
    loading.value = true
    
    // 获取音频轨道
    const tracksResponse = await videoStore.getAudioTracks(props.videoId)
    audioTracks.value = tracksResponse.data?.audio_tracks || []
    
    // 获取字幕
    const subtitlesResponse = await videoStore.getSubtitles(props.videoId)
    subtitles.value = subtitlesResponse.data?.subtitles || []
    
    // 默认选择第一个音频轨道
    if (audioTracks.value.length > 0) {
      selectTrack(audioTracks.value[0])
    }
    
    // 默认选择第一个字幕
    if (subtitles.value.length > 0) {
      selectSubtitle(subtitles.value[0])
    }
    
  } catch (error) {
    console.error('Failed to load audio data:', error)
  } finally {
    loading.value = false
  }
}

const selectTrack = (track) => {
  selectedTrack.value = track
  emit('track-selected', track)
}

const selectSubtitle = async (subtitle) => {
  selectedSubtitle.value = subtitle
  
  try {
    const contentResponse = await videoStore.getSubtitleContent(props.videoId, subtitle.id)
    subtitleContent.value = contentResponse.data?.content || []
  } catch (error) {
    console.error('Failed to load subtitle content:', error)
    subtitleContent.value = []
  }
}

const toggleSubtitlesExpanded = () => {
  subtitlesExpanded.value = !subtitlesExpanded.value
}

const getAudioUrl = (filePath) => {
  // 构建音频文件的URL
  return `/api/v1/files/audio/${encodeURIComponent(filePath)}`
}

const togglePlayback = () => {
  if (audioPlayer.value) {
    if (isPlaying.value) {
      audioPlayer.value.pause()
    } else {
      audioPlayer.value.play()
    }
  }
}

const seekToTime = (time) => {
  if (audioPlayer.value) {
    audioPlayer.value.currentTime = time
  }
}

const onTimeUpdate = () => {
  if (audioPlayer.value) {
    currentTime.value = audioPlayer.value.currentTime
    emit('time-changed', currentTime.value)
  }
}

const onAudioLoaded = () => {
  if (audioPlayer.value) {
    audioDuration.value = audioPlayer.value.duration
  }
}

const onAudioError = (event) => {
  console.error('Audio playback error:', event)
}

// 工具函数
const formatDuration = (seconds) => {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (seconds) => {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatBitrate = (bitrate) => {
  if (!bitrate) return 'Unknown'
  if (bitrate >= 1000) {
    return `${(bitrate / 1000).toFixed(1)} kbps`
  } else {
    return `${bitrate} bps`
  }
}

const getSubtitleTypeText = (type) => {
  const types = {
    manual: '手动上传',
    auto_generated: '自动生成'
  }
  return types[type] || type
}

// 监听器
watch(() => props.videoId, () => {
  if (props.videoId) {
    loadAudioData()
  }
}, { immediate: true })

// 监听音频播放状态
watch(() => audioPlayer.value, (player) => {
  if (player) {
    player.addEventListener('play', () => { isPlaying.value = true })
    player.addEventListener('pause', () => { isPlaying.value = false })
    player.addEventListener('ended', () => { isPlaying.value = false })
  }
})

// 生命周期
onMounted(() => {
  if (props.videoId) {
    loadAudioData()
  }
})
</script>

<style scoped>
/* 自定义音频播放器样式 */
audio::-webkit-media-controls-panel {
  background-color: #f9fafb;
}

audio::-webkit-media-controls-play-button {
  background-color: #3B82F6;
  border-radius: 50%;
}
</style>
