<template>
  <div class="bg-gray-50 rounded-lg p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-3">视频帧时间轴</h3>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
      <span class="ml-2 text-sm text-gray-500">加载帧数据中...</span>
    </div>

    <!-- 帧时间轴 -->
    <div v-else-if="frames.length > 0" class="space-y-4">
      <!-- 时间轴容器 -->
      <div class="relative">
        <!-- 时间轴背景 -->
        <div class="relative h-20 bg-white rounded-lg border overflow-hidden">
          <!-- 帧缩略图 -->
          <div class="flex h-full">
            <div
              v-for="(frame, index) in frames"
              :key="frame.id"
              class="relative flex-shrink-0 cursor-pointer transition-all duration-200 hover:z-10"
              :style="{ width: frameWidth + 'px' }"
              @click="selectFrame(frame)"
              @mouseenter="showTooltip(frame, $event)"
              @mouseleave="hideTooltip"
            >
              <!-- 帧图片 -->
              <img
                :src="getFrameUrl(frame.file_path)"
                :alt="`Frame at ${formatTime(frame.timestamp)}`"
                class="w-full h-full object-cover border-r border-gray-200"
                :class="{
                  'ring-2 ring-primary-500': selectedFrame?.id === frame.id,
                  'opacity-75': !frame.is_key_frame
                }"
                @error="handleImageError"
              />
              
              <!-- 关键帧标识 -->
              <div
                v-if="frame.is_key_frame"
                class="absolute top-1 left-1 w-2 h-2 bg-yellow-400 rounded-full border border-white"
                title="关键帧"
              ></div>
              
              <!-- 时间标签 -->
              <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-xs px-1 py-0.5 text-center">
                {{ formatTime(frame.timestamp) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 播放头 -->
        <div
          v-if="currentTime !== null"
          class="absolute top-0 bottom-0 w-0.5 bg-red-500 pointer-events-none z-20"
          :style="{ left: playheadPosition + 'px' }"
        >
          <div class="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full"></div>
        </div>
      </div>

      <!-- 时间轴控制 -->
      <div class="space-y-3">
        <!-- 时间滑块 -->
        <div class="relative">
          <input
            type="range"
            :min="0"
            :max="videoDuration"
            :step="0.1"
            v-model="currentTime"
            @input="onTimeChange"
            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
          <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>{{ formatTime(0) }}</span>
            <span>{{ formatTime(videoDuration) }}</span>
          </div>
        </div>

        <!-- 当前选中帧信息 -->
        <div v-if="selectedFrame" class="bg-white rounded-lg p-3 border">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">
                帧 #{{ selectedFrame.frame_number }}
                <span v-if="selectedFrame.is_key_frame" class="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded">关键帧</span>
              </h4>
              <p class="text-xs text-gray-500 mt-1">
                时间: {{ formatTime(selectedFrame.timestamp) }} | 
                尺寸: {{ selectedFrame.width }}x{{ selectedFrame.height }} |
                大小: {{ formatFileSize(selectedFrame.file_size) }}
              </p>
            </div>
            <button
              @click="downloadFrame"
              class="text-sm bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded"
            >
              下载
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 无帧数据 -->
    <div v-else class="text-center text-gray-500 py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
      <p class="text-sm">暂无帧数据</p>
      <p class="text-xs mt-1">请先提取关键帧</p>
    </div>

    <!-- 工具提示 -->
    <div
      v-if="tooltip.show"
      class="fixed z-50 bg-black bg-opacity-75 text-white text-xs rounded px-2 py-1 pointer-events-none"
      :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
    >
      {{ tooltip.text }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useVideoStore } from '@/stores/videos'

const props = defineProps({
  videoId: {
    type: Number,
    required: true
  },
  videoDuration: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['frame-selected', 'time-changed'])

const videoStore = useVideoStore()

// 响应式数据
const loading = ref(false)
const frames = ref([])
const selectedFrame = ref(null)
const currentTime = ref(0)
const tooltip = ref({
  show: false,
  x: 0,
  y: 0,
  text: ''
})

// 计算属性
const frameWidth = computed(() => {
  if (frames.value.length === 0) return 0
  // 根据帧数量动态计算宽度，最小60px，最大120px
  const containerWidth = 800 // 假设容器宽度
  const calculatedWidth = containerWidth / frames.value.length
  return Math.max(60, Math.min(120, calculatedWidth))
})

const playheadPosition = computed(() => {
  if (props.videoDuration === 0 || frames.value.length === 0) return 0
  const progress = currentTime.value / props.videoDuration
  return progress * (frameWidth.value * frames.value.length)
})

// 方法
const loadFrames = async () => {
  try {
    loading.value = true
    const response = await videoStore.getVideoFrames(props.videoId)
    // 处理API响应结构
    frames.value = response.data?.frames || []
  } catch (error) {
    console.error('Failed to load frames:', error)
  } finally {
    loading.value = false
  }
}

const selectFrame = (frame) => {
  selectedFrame.value = frame
  currentTime.value = frame.timestamp
  emit('frame-selected', frame)
  emit('time-changed', frame.timestamp)
}

const onTimeChange = () => {
  // 找到最接近当前时间的帧
  if (frames.value.length > 0) {
    const closestFrame = frames.value.reduce((prev, curr) => {
      return Math.abs(curr.timestamp - currentTime.value) < Math.abs(prev.timestamp - currentTime.value) ? curr : prev
    })
    selectedFrame.value = closestFrame
    emit('frame-selected', closestFrame)
  }
  emit('time-changed', currentTime.value)
}

const showTooltip = (frame, event) => {
  tooltip.value = {
    show: true,
    x: event.clientX + 10,
    y: event.clientY - 30,
    text: `帧 #${frame.frame_number} - ${formatTime(frame.timestamp)}${frame.is_key_frame ? ' (关键帧)' : ''}`
  }
}

const hideTooltip = () => {
  tooltip.value.show = false
}

const getFrameUrl = (filePath) => {
  // 构建帧图片的URL，使用相对路径，通过Vite代理
  return `/api/v1/files/frame/${encodeURIComponent(filePath)}`
}

const handleImageError = (event) => {
  // 图片加载失败时的处理
  event.target.src = '/placeholder-frame.png' // 可以设置一个占位图
}

const downloadFrame = () => {
  if (selectedFrame.value) {
    const link = document.createElement('a')
    link.href = getFrameUrl(selectedFrame.value.file_path)
    link.download = `frame_${selectedFrame.value.frame_number}_${formatTime(selectedFrame.value.timestamp)}.jpg`
    link.click()
  }
}

// 工具函数
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 100)
  return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

// 监听器
watch(() => props.videoId, () => {
  if (props.videoId) {
    loadFrames()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.videoId) {
    loadFrames()
  }
})
</script>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
