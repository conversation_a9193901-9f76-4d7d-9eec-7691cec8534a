"""
视频管理API端点
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import FileResponse
from loguru import logger
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import shutil
from app.core.database import get_db
from app.models.task import Video, AudioTrack, Subtitle, VideoFrame
from app.services.video_analysis_service import VideoAnalysisService
from app.services.bitrate_stats_service import BitrateStatsService
from app.services.scene_detection_service import SceneDetectionService
from app.services.subtitle_service import SubtitleService
from app.services.ffmpeg_service import ffmpeg_service

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_videos(db: Session = Depends(get_db)):
    """获取视频列表"""
    videos = db.query(Video).order_by(Video.created_at.desc()).all()
    return [
        {
            "id": video.id,
            "task_id": video.task_id,
            "filename": video.filename,
            "original_filename": video.original_filename,
            "file_size": video.file_size,
            "duration": video.duration,
            "resolution": video.resolution,
            "fps": video.fps,
            "status": video.status,
            "created_at": video.created_at.isoformat()
        }
        for video in videos
    ]


@router.get("/{video_id}", response_model=dict)
async def get_video(video_id: int, db: Session = Depends(get_db)):
    """获取视频详情"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Get related data
    audio_tracks = db.query(AudioTrack).filter(AudioTrack.video_id == video_id).all()
    subtitles = db.query(Subtitle).filter(Subtitle.video_id == video_id).all()
    frame_count = db.query(VideoFrame).filter(VideoFrame.video_id == video_id).count()

    return {
        "id": video.id,
        "task_id": video.task_id,
        "filename": video.filename,
        "original_filename": video.original_filename,
        "file_path": video.file_path,
        "file_size": video.file_size,
        "duration": video.duration,
        "resolution": video.resolution,
        "fps": video.fps,
        "codec": video.codec,
        "bitrate": video.bitrate,
        "key_frame_thumbnail_id": video.key_frame_thumbnail_id,
        "status": video.status,
        "created_at": video.created_at.isoformat(),
        "audio_tracks": [
            {
                "id": track.id,
                "stream_index": track.stream_index,
                "codec_name": track.codec_name,
                "sample_rate": track.sample_rate,
                "channels": track.channels,
                "duration": track.duration,
                "file_path": track.file_path
            }
            for track in audio_tracks
        ],
        "subtitles": [
            {
                "id": subtitle.id,
                "type": subtitle.subtitle_type,
                "language": subtitle.language,
                "confidence": subtitle.confidence,
                "file_path": subtitle.file_path
            }
            for subtitle in subtitles
        ],
        "frame_count": frame_count
    }


@router.post("/{video_id}/analyze")
async def start_analysis(video_id: int, db: Session = Depends(get_db)):
    """开始视频分析"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # 检查视频是否已经在分析中
    if video.status == "analyzing":
        raise HTTPException(status_code=400, detail="Video is already being analyzed")

    # 启动Celery任务进行视频分析
    from app.tasks.video_tasks import process_single_video_complete_analysis

    task = process_single_video_complete_analysis.delay(video_id)

    # 更新视频状态
    video.status = "analyzing"
    db.commit()

    return {
        "message": "Analysis started",
        "video_id": video_id,
        "status": "analyzing",
        "task_id": task.id
    }


@router.post("/{video_id}/retry")
async def retry_analysis(video_id: int, db: Session = Depends(get_db)):
    """重试视频分析"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # 检查视频状态是否为失败
    if video.status != "failed":
        raise HTTPException(status_code=400, detail="Video analysis is not in failed state")

    # 重置视频状态并启动新的分析任务
    from app.tasks.video_tasks import process_single_video_complete_analysis
    task = process_single_video_complete_analysis.delay(video_id)

    # 更新视频状态
    video.status = "analyzing"

    # 如果有关联的任务，也重置任务状态
    if video.task_id:
        from app.models.task import Task
        task_obj = db.query(Task).filter(Task.id == video.task_id).first()
        if task_obj:
            task_obj.status = "processing"
            task_obj.progress = 0.0
            task_obj.error_message = None

    db.commit()

    return {
        "message": "Analysis retry started",
        "video_id": video_id,
        "status": "analyzing",
        "task_id": task.id
    }


@router.post("/{video_id}/comprehensive-analysis")
async def start_comprehensive_analysis(video_id: int, db: Session = Depends(get_db)):
    """开始详细视频分析，生成包含packets和frames的完整JSON信息"""
    from app.services.video_analysis_service import VideoAnalysisService

    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    try:
        # 创建分析服务实例
        analysis_service = VideoAnalysisService(db)

        # 生成详细分析信息
        json_file_path = analysis_service.generate_comprehensive_video_info(video_id)

        # 获取生成的JSON文件信息
        import os
        file_size = os.path.getsize(json_file_path)
        json_filename = os.path.basename(json_file_path)

        return {
            "message": "Comprehensive analysis completed",
            "video_id": video_id,
            "json_file": json_filename,
            "json_file_path": json_file_path,
            "file_size_mb": round(file_size / (1024*1024), 2),
            "status": "completed"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Comprehensive analysis failed: {str(e)}"
        )


@router.get("/{video_id}/comprehensive-info")
async def get_comprehensive_info(video_id: int, db: Session = Depends(get_db)):
    """获取视频的详细分析JSON信息"""
    import os
    import json

    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # 构建JSON文件路径
    video_filename = os.path.splitext(video.filename)[0]
    json_filename = f"{video_filename}.json"
    storage_dir = os.path.dirname(video.file_path)
    json_file_path = os.path.join(storage_dir, json_filename)

    if not os.path.exists(json_file_path):
        raise HTTPException(
            status_code=404,
            detail="Comprehensive analysis not found. Please run comprehensive analysis first."
        )

    try:
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            comprehensive_data = json.load(f)

        # 获取文件大小信息
        file_size = os.path.getsize(json_file_path)

        return {
            "video_id": video_id,
            "json_file": json_filename,
            "file_size_mb": round(file_size / (1024*1024), 2),
            "data": comprehensive_data
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to read comprehensive info: {str(e)}"
        )


@router.get("/{video_id}/thumbnail")
async def get_video_thumbnail(video_id: int, db: Session = Depends(get_db)):
    """获取视频缩略图"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    # Use key frame thumbnail if available
    if video.key_frame_thumbnail_id:
        key_frame = db.query(VideoFrame).filter(VideoFrame.id == video.key_frame_thumbnail_id).first()
        if key_frame and key_frame.file_path and os.path.exists(key_frame.file_path):
            return FileResponse(key_frame.file_path, media_type="image/jpeg")

    # Fallback: try to find any key frame for this video
    fallback_frame = db.query(VideoFrame).filter(
        VideoFrame.video_id == video_id,
        VideoFrame.is_key_frame == True
    ).order_by(VideoFrame.timestamp).first()

    if fallback_frame and fallback_frame.file_path and os.path.exists(fallback_frame.file_path):
        return FileResponse(fallback_frame.file_path, media_type="image/jpeg")

    # No thumbnail available
    raise HTTPException(status_code=404, detail="Thumbnail not found")


@router.post("/{video_id}/analyze-metadata")
async def analyze_video_metadata(video_id: int, db: Session = Depends(get_db)):
    """分析视频元数据"""
    # Check dependencies first
    deps = ffmpeg_service.check_dependencies()
    if not deps["ffprobe"]:
        raise HTTPException(status_code=500, detail="ffprobe not available")

    try:
        analysis_service = VideoAnalysisService(db)
        metadata = analysis_service.analyze_video_metadata(video_id)

        return {
            "message": "Video metadata analyzed successfully",
            "video_id": video_id,
            "metadata": metadata
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except FileNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.post("/{video_id}/extract-audio")
async def extract_audio_tracks(video_id: int, db: Session = Depends(get_db)):
    """提取音频轨道"""
    # Check dependencies first
    deps = ffmpeg_service.check_dependencies()
    if not deps["ffmpeg"]:
        raise HTTPException(status_code=500, detail="ffmpeg not available")

    try:
        analysis_service = VideoAnalysisService(db)

        # Extract audio tracks using organized file structure
        extracted_files = analysis_service.extract_audio_tracks(video_id)

        return {
            "message": "Audio tracks extracted successfully",
            "video_id": video_id,
            "extracted_files": extracted_files
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Audio extraction failed: {str(e)}")


@router.post("/{video_id}/extract-key-frames")
async def extract_video_key_frames(
    video_id: int,
    db: Session = Depends(get_db)
):
    """提取视频关键帧"""
    # Check dependencies first
    deps = ffmpeg_service.check_dependencies()
    if not deps["ffmpeg"]:
        raise HTTPException(status_code=500, detail="ffmpeg not available")

    try:
        analysis_service = VideoAnalysisService(db)

        # Extract key frames using organized file structure

        frame_files = analysis_service.extract_video_key_frames(video_id)

        return {
            "message": "Video key frames extracted successfully",
            "video_id": video_id,
            "frame_count": len(frame_files),
            "frame_files": frame_files,
            "key_frames_only": True
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Frame extraction failed: {str(e)}")


@router.post("/{video_id}/upload-subtitle")
async def upload_subtitle(
    video_id: int,
    subtitle_file: UploadFile = File(...),
    language: str = Form("zh-cn"),
    db: Session = Depends(get_db)
):
    """上传字幕文件"""
    if not subtitle_file.filename.endswith('.srt'):
        raise HTTPException(status_code=400, detail="Only SRT files are supported")

    try:
        # Save uploaded file
        upload_dir = f"uploads/subtitles/video_{video_id}"
        os.makedirs(upload_dir, exist_ok=True)

        file_path = os.path.join(upload_dir, subtitle_file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(subtitle_file.file, buffer)

        # Process subtitle
        subtitle_service = SubtitleService(db)
        subtitle = subtitle_service.upload_manual_subtitle(video_id, file_path, language)

        return {
            "message": "Subtitle uploaded successfully",
            "video_id": video_id,
            "subtitle_id": subtitle.id,
            "language": language,
            "file_path": file_path
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Subtitle upload failed: {str(e)}")


@router.post("/{video_id}/generate-subtitle")
async def generate_automatic_subtitle(
    video_id: int,
    audio_track_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """生成自动字幕"""
    try:
        subtitle_service = SubtitleService(db)
        subtitle = subtitle_service.generate_automatic_subtitle(video_id, audio_track_id)

        return {
            "message": "Automatic subtitle generated successfully",
            "video_id": video_id,
            "subtitle_id": subtitle.id,
            "confidence": subtitle.confidence,
            "processing_time": subtitle.processing_time
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Subtitle generation failed: {str(e)}")


@router.get("/{video_id}/audio-tracks")
async def get_audio_tracks(video_id: int, db: Session = Depends(get_db)):
    """获取视频的音频轨道列表"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    audio_tracks = db.query(AudioTrack).filter(AudioTrack.video_id == video_id).all()

    return {
        "status": "success",
        "data": {
            "video_id": video_id,
            "audio_tracks": [
                {
                    "id": track.id,
                    "stream_index": track.stream_index,
                    "codec_name": track.codec_name,
                    "codec_long_name": track.codec_long_name,
                    "sample_rate": track.sample_rate,
                    "channels": track.channels,
                    "channel_layout": track.channel_layout,
                    "bit_rate": track.bit_rate,
                    "duration": track.duration,
                    "file_path": track.file_path,
                    "tags": track.tags,
                    "created_at": track.created_at.isoformat()
                }
                for track in audio_tracks
            ]
        }
    }


@router.get("/{video_id}/subtitles")
async def get_subtitles(video_id: int, db: Session = Depends(get_db)):
    """获取视频的字幕列表"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    subtitles = db.query(Subtitle).filter(Subtitle.video_id == video_id).all()

    return {
        "status": "success",
        "data": {
            "video_id": video_id,
            "subtitles": [
                {
                    "id": subtitle.id,
                    "audio_track_id": subtitle.audio_track_id,
                    "subtitle_type": subtitle.subtitle_type,
                    "language": subtitle.language,
                    "confidence": subtitle.confidence,
                    "processing_time": subtitle.processing_time,
                    "file_path": subtitle.file_path,
                    "created_at": subtitle.created_at.isoformat()
                }
                for subtitle in subtitles
            ]
        }
    }


@router.get("/{video_id}/subtitles/{subtitle_id}/content")
async def get_subtitle_content(video_id: int, subtitle_id: int, db: Session = Depends(get_db)):
    """获取字幕内容"""
    subtitle = db.query(Subtitle).filter(
        Subtitle.id == subtitle_id,
        Subtitle.video_id == video_id
    ).first()

    if not subtitle:
        raise HTTPException(status_code=404, detail="Subtitle not found")

    try:
        import json
        # 解析字幕内容
        if subtitle.content:
            content = json.loads(subtitle.content) if isinstance(subtitle.content, str) else subtitle.content
        else:
            content = []

        return {
            "status": "success",
            "data": {
                "subtitle_id": subtitle.id,
                "video_id": video_id,
                "subtitle_type": subtitle.subtitle_type,
                "language": subtitle.language,
                "confidence": subtitle.confidence,
                "content": content
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get subtitle content: {str(e)}")


@router.get("/{video_id}/analysis-summary")
async def get_video_analysis_summary(video_id: int, db: Session = Depends(get_db)):
    """获取视频分析汇总"""
    try:
        analysis_service = VideoAnalysisService(db)
        summary = analysis_service.get_video_analysis_summary(video_id)

        return summary
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get analysis summary: {str(e)}")


@router.get("/dependencies/check")
async def check_dependencies():
    """检查系统依赖"""
    deps = ffmpeg_service.check_dependencies()

    return {
        "dependencies": deps,
        "all_available": all(deps.values()),
        "message": "All dependencies available" if all(deps.values()) else "Some dependencies missing"
    }


@router.post("/{video_id}/bitrate-analysis")
async def analyze_video_bitrate(
    video_id: int,
    stream_type: str = "video",
    aggregation: str = "time",
    chunk_size: float = 30.0,
    plot_width: int = 70,
    plot_height: int = 18,
    db: Session = Depends(get_db)
):
    """分析视频比特率统计"""
    try:
        analysis_service = VideoAnalysisService(db)
        bitrate_stats = analysis_service.analyze_video_bitrate_stats(
            video_id=video_id,
            stream_type=stream_type,
            aggregation=aggregation,
            chunk_size=chunk_size,
            plot_width=plot_width,
            plot_height=plot_height
        )

        return {
            "status": "success",
            "message": f"Bitrate analysis completed for video {video_id}",
            "data": {
                "video_id": bitrate_stats.video_id,
                "stream_type": bitrate_stats.stream_type,
                "avg_fps": bitrate_stats.avg_fps,
                "num_frames": bitrate_stats.num_frames,
                "avg_bitrate": bitrate_stats.avg_bitrate,
                "max_bitrate": bitrate_stats.max_bitrate,
                "min_bitrate": bitrate_stats.min_bitrate,
                "duration": bitrate_stats.duration,
                "aggregation": bitrate_stats.aggregation,
                "chunk_size": bitrate_stats.chunk_size
            }
        }

    except Exception as e:
        logger.error(f"Failed to analyze bitrate for video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{video_id}/bitrate-stats")
async def get_video_bitrate_stats(
    video_id: int,
    db: Session = Depends(get_db)
):
    """获取视频比特率统计信息"""
    try:
        analysis_service = VideoAnalysisService(db)
        stats_summary = analysis_service.get_bitrate_stats_summary(video_id)

        if not stats_summary:
            raise HTTPException(
                status_code=404,
                detail="Bitrate statistics not found. Please run bitrate analysis first."
            )

        return {
            "status": "success",
            "data": stats_summary
        }

    except Exception as e:
        logger.error(f"Failed to get bitrate stats for video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{video_id}/bitrate-plot")
async def get_video_bitrate_plot(
    video_id: int,
    db: Session = Depends(get_db)
):
    """获取视频比特率ASCII图表"""
    try:
        bitrate_service = BitrateStatsService(db)
        bitrate_stats = bitrate_service.get_bitrate_stats(video_id)

        if not bitrate_stats:
            raise HTTPException(
                status_code=404,
                detail="Bitrate statistics not found. Please run bitrate analysis first."
            )

        return {
            "status": "success",
            "data": {
                "plot_data": bitrate_stats.plot_data,
                "plot_width": bitrate_stats.plot_width,
                "plot_height": bitrate_stats.plot_height,
                "video_id": bitrate_stats.video_id,
                "stream_type": bitrate_stats.stream_type,
                "aggregation": bitrate_stats.aggregation,
                "chunk_size": bitrate_stats.chunk_size
            }
        }

    except Exception as e:
        logger.error(f"Failed to get bitrate plot for video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{video_id}/frames")
async def get_video_frames(video_id: int, db: Session = Depends(get_db)):
    """获取视频帧列表"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    try:
        frames = db.query(VideoFrame).filter(
            VideoFrame.video_id == video_id
        ).order_by(VideoFrame.timestamp).all()

        frame_list = []
        for frame in frames:
            frame_list.append({
                "id": frame.id,
                "frame_number": frame.frame_number,
                "timestamp": frame.timestamp,
                "file_path": frame.file_path,
                "width": frame.width,
                "height": frame.height,
                "file_size": frame.file_size,
                "is_key_frame": frame.is_key_frame,
                "created_at": frame.created_at.isoformat()
            })

        return {
            "status": "success",
            "data": {
                "video_id": video_id,
                "frame_count": len(frame_list),
                "frames": frame_list
            }
        }

    except Exception as e:
        logger.error(f"Failed to get frames for video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get frames: {str(e)}")


@router.get("/{video_id}/scenes")
async def get_video_scenes(video_id: int, db: Session = Depends(get_db)):
    """获取视频场景检测结果"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    try:
        scene_service = SceneDetectionService(db)
        scenes_summary = scene_service.get_scenes_summary(video_id)

        return {
            "status": "success",
            "data": scenes_summary
        }

    except Exception as e:
        logger.error(f"Failed to get scenes for video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get scenes: {str(e)}")


@router.post("/{video_id}/scenes/detect")
async def detect_video_scenes(
    video_id: int,
    detector_type: str = "content",
    threshold: float = 30.0,
    min_scene_len: float = 1.0,
    db: Session = Depends(get_db)
):
    """手动触发场景检测"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    try:
        scene_service = SceneDetectionService(db)
        scenes = scene_service.detect_scenes(
            video_id=video_id,
            detector_type=detector_type,
            threshold=threshold,
            min_scene_len=min_scene_len
        )

        return {
            "status": "success",
            "message": f"Detected {len(scenes)} scenes",
            "data": {
                "total_scenes": len(scenes),
                "scenes": scenes
            }
        }

    except Exception as e:
        logger.error(f"Failed to detect scenes for video {video_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to detect scenes: {str(e)}")
