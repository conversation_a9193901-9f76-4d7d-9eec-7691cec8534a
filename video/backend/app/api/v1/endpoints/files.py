"""
文件服务API端点
"""

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import os
import urllib.parse
from app.core.database import get_db
from app.models.task import VideoFrame

router = APIRouter()


@router.get("/frame/{file_path:path}")
async def get_frame_file(file_path: str, db: Session = Depends(get_db)):
    """获取帧图片文件"""
    try:
        # URL解码文件路径
        decoded_path = urllib.parse.unquote(file_path)
        
        # 验证文件是否存在于数据库中
        frame = db.query(VideoFrame).filter(VideoFrame.file_path == decoded_path).first()
        if not frame:
            raise HTTPException(status_code=404, detail="Frame not found in database")
        
        # 检查文件是否存在
        if not os.path.exists(decoded_path):
            raise HTTPException(status_code=404, detail="Frame file not found on disk")
        
        # 返回文件
        return FileResponse(
            decoded_path,
            media_type="image/jpeg",
            filename=f"frame_{frame.frame_number}.jpg"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to serve frame file: {str(e)}")


@router.get("/audio/{file_path:path}")
async def get_audio_file(file_path: str):
    """获取音频文件"""
    try:
        # URL解码文件路径
        decoded_path = urllib.parse.unquote(file_path)
        
        # 检查文件是否存在
        if not os.path.exists(decoded_path):
            raise HTTPException(status_code=404, detail="Audio file not found")
        
        # 确定媒体类型
        if decoded_path.endswith('.mp3'):
            media_type = "audio/mpeg"
        elif decoded_path.endswith('.wav'):
            media_type = "audio/wav"
        elif decoded_path.endswith('.aac'):
            media_type = "audio/aac"
        else:
            media_type = "audio/mpeg"  # 默认
        
        # 返回文件
        return FileResponse(
            decoded_path,
            media_type=media_type,
            filename=os.path.basename(decoded_path)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to serve audio file: {str(e)}")


@router.get("/video/{file_path:path}")
async def get_video_file(file_path: str):
    """获取视频文件"""
    try:
        # URL解码文件路径
        decoded_path = urllib.parse.unquote(file_path)
        
        # 检查文件是否存在
        if not os.path.exists(decoded_path):
            raise HTTPException(status_code=404, detail="Video file not found")
        
        # 确定媒体类型
        if decoded_path.endswith('.mp4'):
            media_type = "video/mp4"
        elif decoded_path.endswith('.avi'):
            media_type = "video/x-msvideo"
        elif decoded_path.endswith('.mov'):
            media_type = "video/quicktime"
        elif decoded_path.endswith('.mkv'):
            media_type = "video/x-matroska"
        else:
            media_type = "video/mp4"  # 默认
        
        # 返回文件
        return FileResponse(
            decoded_path,
            media_type=media_type,
            filename=os.path.basename(decoded_path)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to serve video file: {str(e)}")
