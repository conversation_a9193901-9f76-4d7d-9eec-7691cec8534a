"""
API v1 路由汇总
"""

from fastapi import APIRouter
from app.api.v1.endpoints import tasks, videos, analysis, clips, files

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(videos.router, prefix="/videos", tags=["videos"])
api_router.include_router(analysis.router, prefix="/analysis", tags=["analysis"])
api_router.include_router(clips.router, prefix="/clips", tags=["clips"])
api_router.include_router(files.router, prefix="/files", tags=["files"])
